## 评估Prompt优秀性的关键方面

评估一个Prompt是否优秀，需要从以下几个关键方面进行：

### 1.清晰度与具体性 (Clarity and Specificity)

* **评估点:** Prompt的语言是否清晰、无歧义？指令是否足够具体，让模型能够准确理解任务要求？是否包含了执行任务所需的关键信息？对于需要创造性输出的任务，是否提供了恰当的引导和边界？
* **重要性:** 模糊或过于宽泛的Prompt容易导致模型输出偏离主题、内容空泛或不完整。明确的指令是高质量输出的基础。

### 2.相关性与任务完成度 (Relevance and Task Completion)

* **评估点:** 模型生成的输出内容是否与Prompt提出的问题或任务高度相关？是否完整地回答了所有隐含或明确的问题？是否执行了所有要求的操作？
* **重要性:** 这是衡量Prompt有效性的核心指标。输出必须直接回应Prompt的核心诉求。

### 3.准确性与事实性 (Accuracy and Factuality)

* **评估点:** 对于需要基于事实进行回答或生成的任务（如知识问答、报告撰写），输出内容的准确性如何？是否存在事实错误、误导性信息或“幻觉” (Hallucination)？
* **重要性:** 在许多应用场景下，如教育、医疗、金融等，信息的准确性是不可或缺的。

### 4.结构与格式遵循度 (Structure and Format Compliance)

* **评估点:** 如果Prompt对输出的结构或格式有特定要求（例如，要求生成列表、JSON对象、代码片段、特定段落格式、某种语气或风格），模型是否严格遵循了这些要求？
* **重要性:** 保证输出的可用性和一致性，便于后续处理或直接使用。

### 5.一致性与可靠性 (Consistency and Reliability)

* **评估点:** 多次使用同一个Prompt（或者进行微小、合理的改动后），模型输出的质量和风格是否保持相对稳定？结果是否具有一定的可预测性？
* **重要性:** 对于需要稳定输出的应用场景（如自动化报告、客服机器人）非常关键。虽然LLM本身存在随机性，但好的Prompt能引导其产生更可靠的结果。

### 6.效率与简洁性 (Efficiency and Conciseness)

* **评估点:** Prompt本身是否简洁明了，避免了不必要的冗余信息？它是否能高效地引导模型达成目标，减少了不必要的交互轮次或Token消耗？输出内容是否在满足要求的前提下足够简洁（如果简洁性是目标之一）？
* **重要性:** 提高人机交互效率，降低潜在的使用成本。

### 7.安全性与偏见控制 (Safety and Bias Control)

* **评估点:** Prompt是否可能引导模型生成不安全、有害、歧视性、攻击性或带有严重偏见的内容？Prompt的设计是否考虑到了伦理和社会责任？
* **重要性:** 这是负责任地使用AI技术的基本要求，避免产生负面社会影响。

### 8.鲁棒性 (Robustness)

* **评估点:** Prompt对于输入中的微小变化或干扰（例如，同义词替换、语序微调）是否具有一定的抵抗力？在稍微不同的、但意图相同的提问下，是否仍能产生合理、一致的结果？
* **重要性:** 衡量Prompt在实际应用中的稳定性和适应性。

### 9.用户友好度 (User Friendliness)

* **评估点:** Prompt本身的措辞和结构是否易于人类用户理解和使用？非专业用户是否也能相对容易地理解其意图并可能进行微调？
* **重要性:** 尤其在需要用户直接交互或修改Prompt的应用中，良好的用户友好度能提升使用体验和效率。

### 10.创新性引导 (Creativity Guidance)

* **评估点:** Prompt是否明确鼓励或暗示需要创新性、原创性或想象力？是否提供了激发创意的开放性空间或有趣的约束？是否能有效引导模型打破常规思维，生成新颖、独特或富有想象力的内容？输出是否体现了与任务目标相符的创造性？
* **重要性:** 对于头脑风暴、内容创作、艺术设计、概念构思、解决需要非传统思路的问题等任务至关重要。有助于充分发挥LLM的生成潜力，探索新的可能性。




请根据以下10个评估维度，对后面提供的 [待评估的Prompt] 进行分析和评估。

重要提示：
1. 一个优秀的Prompt不一定需要在所有维度上都表现完美；
2. 请根据 [待评估的Prompt] 的具体目标、内容和预期应用场景，判断哪些维度最为关键，并侧重分析这些方面；
3. 对于明显不适用的维度（例如，一个纯粹获取事实信息的Prompt不需要评估创新性引导），可以明确指出其不适用性；
4. 参考优先级建议：对于事实性问答，重点关注维度 1、2、3；对于创意写作，重点关注维度 1、2、10；对于格式化输出任务，重点关注维度 1、4；

评估维度：

1. 清晰度与具体性 : Prompt是否清晰、无歧义？指令是否具体？是否包含关键信息？
2. 相关性与任务完成度：Prompt是否能引导模型生成高度相关的内容？是否能完整地完成预期任务？
3. 准确性与事实性： (若适用) Prompt是否可能引导产生事实准确的输出？是否有效避免“幻觉”？
4. 结构与格式遵循度： (若适用) Prompt是否明确了输出的结构或格式要求？模型是否可能遵循？
5. 一致性与可靠性 ：Prompt的设计是否有利于产生相对稳定、可预测的结果（在合理的模型随机性范围内）？
6. 效率与简洁性：Prompt本身是否简洁、避免冗余？是否能高效引导模型，可能减少Token消耗？
7. 安全性与偏见控制： Prompt是否有效避免引导产生不安全、有害或带有严重偏见的内容？
8. 鲁棒性：Prompt对措辞的微小变动或相似问法是否具有一定的抵抗力，能保持输出意图的一致性？
9. 用户友好度：Prompt是否易于人类用户理解、使用或修改（尤其在需要交互或调整的场景下）？
10. 创新性引导 ： (若适用) 对于需要创造性输出的任务，Prompt是否能有效激发模型的想象力和创造力，同时提供足够的引导以避免输出过于发散或不相关？

输出格式要求： 请按维度逐一评估，针对每个维度给出评分（1-5分，5分为最高）并附上简要说明（1-2句话），以表格形式输出。对于不适用的维度，请标注“不适用”并说明原因。最后，提供总体评价（1-10分）和1-2条具体的改进建议（需要的话）。

---
[待评估的Prompt]:
[这里粘贴评估的目标Prompt文本]
