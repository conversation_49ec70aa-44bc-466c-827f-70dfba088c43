原文链接: https://mp.weixin.qq.com/s/yhILmDtTzhQvQYylpsC9vA

今天带大家深入解读一份超实用指南，微软研究院和 Salesforce 研究院的研究团队发表的一篇重要论文《LLMs Get Lost In Multi-Turn Conversation》，即如何提升大模型在多轮对话的输出质量。



## 研究背景与核心发现

来自微软研究院和 Salesforce 研究院的研究团队发表了一篇重要论文，揭示了大型语言模型（LLMs）在多轮对话中存在的严重问题。

研究团队对 15 个顶级大语言模型进行了大规模实验，包括 GPT-4.1、Gemini 2.5 Pro、Claude 3.7 Sonnet 等最先进的模型，结果显示所有测试的 LLMs 在多轮对话中的表现都显著低于单轮对话，平均性能下降 39%。

这个 39%的下降幅度意味着什么？想象一下你正在和一个助手协作完成一个项目。如果这个助手在你一次性给出所有要求时能达到 90%的满意度，那么当你分几次逐步说明要求时，他的表现可能只有 55%的满意度。

这就像一个技艺高超的厨师，如果你一次性告诉他要做什么菜、用什么食材、什么口味，他能做出很棒的菜；

但如果你先说要做菜，然后再说要用鸡肉，接着说要辣一点，最后说要川菜风格，他可能就做得一塌糊涂了。

分析 200,000+模拟对话，将性能下降分解为两个组成部分：

能力的轻微损失和不可靠性的显著增加，即大模型的自身能力和大模型输出的内容质量

研究发现，从小型开源模型（Llama3.1-8B-Instruct）到最先进的商业模型（Gemini 2.5 Pro），都出现了这种现象，甚至在仅有两轮的对话中就开始出现性能下降。

## "在对话中迷失"现象的定义

研究团队将这种现象命名为"lost in conversation phenomenon"（在对话中迷失）：当 LLMs 在多轮对话中走错方向时，它们会迷失并且无法恢复，迷失即即输出质量差。

为了理解这个问题，让我们看看论文中的具体例子。研究人员将完整的 GSM8K 数学题目拆分成多个片段（shards），逐步向 AI 透露信息：

完整指令（original）： "Jay 正在制作雪球准备和姐姐打雪仗。他每小时能做 20 个雪球，但每 15 分钟会融化 2 个。要做到 60 个雪球需要多长时间？"

拆分指令（sharded instruction）：

- Shard 1: "Jay 要为雪仗做准备需要多长时间？"
- Shard 2: "他在和姐姐准备打雪仗"
- Shard 3: "他每小时能做 20 个雪球"
- Shard 4: "他想要 60 个雪球"
- Shard 5: "问题是每 15 分钟会融化 2 个"

这种设计巧妙地模拟了现实中的情况。在日常生活中，我们很少一开始就能完整、准确地描述我们的需求。

更常见的是，我们先提出一个大概的想法，然后在交流过程中逐步澄清细节。

比如你想装修房子，可能先说"我想装修客厅"，然后说"要现代风格的"，接着说"预算在 10 万以内"，最后说"希望两个月内完成"。

这种逐步澄清的交流方式对人类来说很自然，但对 AI 来说却是一个巨大的挑战。

## 实验设计：Sharded Simulation

研究团队设计了"分片模拟"（Sharded Simulation）实验框架，创建了五种不同的对话模拟类型：

![img](evernotecid://FFD5F938-E235-4879-B696-77F3A2444BC3/appyinxiangcom/10059513/ENResource/p11170)

- FULLY-SPECIFIED （FULL）：一次性提供完整指令的单轮对话
- SHARDED：真正的多轮逐步披露信息
- CONCAT：将分片信息合并成列表形式的单轮对话
- RECAP：在 SHARDED 对话最后增加重述所有信息的一轮
- SNOWBALL：每轮都重复之前的所有信息

这种实验设计就像做医学对照试验一样严谨。通过 CONCAT 模拟，研究者可以确认性能下降不是因为信息重新表述造成的，而是真正由多轮交互本身导致的。

这就像测试一种药物的效果时，需要区分到底是药物本身有效，还是病人心理暗示的作用。

论文显示 CONCAT 性能平均达到 FULL 性能的 95.1%，这证明了问题确实出在多轮交互上，而不是信息重新组织造成的。

## 六大任务领域的测试

研究涵盖了六个不同的任务领域，构建了 600 个分片指令：

![img](evernotecid://FFD5F938-E235-4879-B696-77F3A2444BC3/appyinxiangcom/10059513/ENResource/p11173)



1. 1. 编程任务（Code）

基于 HumanEval 和 LiveCodeBench 数据集，要求 AI 编写 Python 函数。使用功能准确性（Functional Accuracy）评估。

1. 2. 数据库查询（Database）

基于 Spider 数据集，要求 AI 根据自然语言描述生成 SQL 查询。使用功能准确性评估。

1. 3. API 调用（Actions）

基于 Berkeley Function Calling Leaderboard，要求 AI 生成正确的 API 调用序列。使用精确匹配（Exact Match）评估。

1. 4. 数学计算（Math）

基于 GSM8K 数据集的小学数学应用题。使用精确匹配评估。

1. 5. 数据转文本（Data-to-Text）

基于 ToTTo 数据集，要求 AI 为表格数据生成自然语言描述。使用 BLEU 评估。

1. 6. 文档摘要（Summary）

基于 Summary of a Haystack 数据集，要求 AI 对多个文档进行摘要并提供引用。使用 Coverage & Citation 评估。

这六个任务的选择覆盖了从纯逻辑推理到创意表达的各个方面。就像测试一个人的综合能力，不能只考数学，还要考语文、英语、科学等各个科目。

这样全面的测试确保了研究结论的普遍适用性，不是某个特定领域的问题，而是 AI 系统的根本性缺陷。

## 性能评估指标：Aptitude vs Reliability

研究定义了三个关键指标来分析性能：

- 平均性能 （P）：所有模拟的平均得分
- 能力 （A90）：90 百分位数得分，表示最佳情况下的性能
- 不可靠性 （U90-10）：90 百分位数与 10 百分位数之间的差值

研究将性能下降分解为两个独立的维度：能力（Aptitude）和可靠性（Reliability）。从单轮到多轮对话，模型的能力只下降了约 15%，但可靠性的下降高达 112%。

这个发现用一个简单的比喻来理解：想象有两个射箭手。射箭手 A 的最好成绩是 9 环，但有时候只能射到 6 环，表现很不稳定；射箭手 B 的最好成绩是 8 环，但每次都能射到 7-8 环，非常稳定。

从平均成绩看，A 可能比 B 高一点，但 B 显然更可靠。

在 AI 的情况下，Aptitude（能力）就是最好成绩（90 百分位数），Reliability（可靠性）就是表现的一致性。

研究发现，多轮对话中的 AI 就像那个不稳定的射箭手 A，虽然有时还能表现很好，但你永远不知道下次会怎样。

## 实验规模与结果

研究对 15 个 LLMs 进行了测试，包括 OpenAI （GPT-4o-mini， GPT-4o， o3， GPT-4.1）、Anthropic （Claude 3 Haiku， Claude 3.7 Sonnet）、Google （Gemini 2.5 Flash， Gemini 2.5 Pro）、Meta （Llama3.1-8B-Instruct， Llama3.3-70B-Instruct， Llama 4 Scout）、AI2 OLMo-2-13B、Microsoft Phi-4、Deepseek-R1 和 Cohere Command-A。

每个（模型、指令、模拟类型）组合进行了 N=10 次模拟，总计超过 200,000 次对话模拟。研究发现：

- 每个模型在每个任务上都出现了 FULL 到 SHARDED 的性能下降
- 平均性能下降为 -39%
- CONCAT 性能平均为 FULL 性能的 95.1%，确认了多轮交互本身是问题根源

这就像发现了一个"普遍定律"：不管是什么品牌的 AI，不管是处理什么类型的任务，只要是多轮对话，性能就会下降。

这种普遍性说明这不是某个特定模型的 bug，而是当前 AI 技术的根本性局限。

## 渐进式分片实验

研究团队进行了"渐进式分片"实验，将 31 个指令分别拆分成 2 到 8 个不同数量的片段。使用 GPT-4o 和 GPT-4o-mini 进行测试，结果显示：

- 只要是多轮对话（2 个或以上片段），性能下降就会发生
- 分片的具体数量对下降幅度影响不大
- 即使是两轮对话也会导致模型迷失

这就像发现了一个临界点：不管你是把一个任务分成 2 步还是 8 步，只要是分步进行，问题就会出现。

这说明不是"步骤太多"的问题，而是"分步骤"本身的问题。

对用户来说，这意味着不要指望通过"更仔细地分步骤"来改善 AI 的表现，关键还是要尽量在一开始就提供完整的信息。

## LLMs 迷失（输出质量降低）的四大根本原因

![img](evernotecid://FFD5F938-E235-4879-B696-77F3A2444BC3/appyinxiangcom/10059513/ENResource/p11174)



### 过早的答案尝试（Premature Answer Attempts）

研究分析了对话中首次答案尝试的时机，发现：

- 在对话前 20%时间就尝试回答的情况，平均性能只有 30.9 分
- 等到对话后 20%才开始回答的情况，平均性能能达到 64.4 分
- 所有模型都显示出这种模式

这就像一个急性子的学生，老师还没把题目说完，他就急着举手回答。

比如老师说"小明有 10 个苹果..."，学生就抢着说"答案是 10！"

但其实完整的题目是"小明有 10 个苹果，吃掉了 3 个，又买了 5 个，现在有几个？"

这种急于求成的态度在人类身上我们叫"冲动"，在 AI 身上就是"过早的答案尝试"。

### 答案膨胀现象（Answer Bloat）

研究发现，在多轮对话中：

- 随着对话的进行，AI 生成的答案变得越来越长
- 到对话结束时，多轮对话中的答案通常比单轮对话中的答案长 20-300%
- 即使是正确的答案也变得更加冗长（代码任务中长 27%，数据库任务中长 14%）

这就像一个人越解释越糊涂。比如你问路："请问图书馆怎么走？

"一开始他说"直走 200 米左转"，但后来又补充"不对，可能是 300 米，

或者你也可以从后门进，那样的话先右转，然后..."

最后说了一大堆，反而把你绕糊涂了。

AI 在多轮对话中也是这样，越说越复杂，越来越偏离重点。

### 中间信息遗失（Loss-in-Middle-Turns）

通过分析摘要任务中的引用模式，研究发现：

- AI 倾向于关注对话的开头和结尾
- 在 8 轮对话中，AI 对第 8 轮（最后一轮）文档的引用率是 20%
- 对第 1 轮文档的引用率相对较高
- 但对第 2、3 轮文档的引用率只有 8%

这就像人类的记忆特点：我们往往记得一件事的开头和结尾，但容易忘记中间的部分。

比如看一部电影，你可能记得开头的精彩场面和结尾的高潮，但中间的情节可能模糊。

但对 AI 来说，这种"选择性注意"是有害的，因为在复杂任务中，中间步骤往往包含关键信息。

### 过度冗长的回应（Overly Verbose Responses）

研究将对话按回应长度分为五个等级（最短、短、中等、长、最长），发现：

- 在六个任务中的五个任务中，生成最短回应的模型表现最好
- 生成最长回应的模型表现最差
- 性能差异可达 10-50%

这挑战了"更详细就更好"的直觉。就像一个老师，如果他能用简单明了的话解释清楚一个概念，说明他真正理解了；

如果他需要绕来绕去说一大堆，可能他自己都没搞清楚。冗长的回应往往包含更多的假设和推测，这些额外的内容反而会在后续对话中造成混乱。

## 温度参数调节的实验

研究专门测试了降低温度参数（temperature）对性能的影响：

实验设置：

- 测试了助手温度（AT）= 1.0， 0.5， 0.0 三种设置
- 对于 SHARDED 模拟，还测试了用户温度（UT）的三种设置
- 使用 GPT-4o 和 GPT-4o-mini 进行测试

结果发现：

- 在 FULL 和 CONCAT 设置中，降低温度确实能显著提高可靠性（不可靠性下降 50-80%）
- 但在 SHARDED 设置中，GPT-4o-mini 的可靠性没有改善，GPT-4o 只有轻微改善（15-20%）
- 即使在温度=0 的最严格设置下，不可靠性仍然高达 30%左右

这就像发现一个演员在台上表演不稳定，你以为是因为他太紧张了，所以给他吃药让他不紧张。但结果发现，即使他完全不紧张了，表演还是不稳定。

这说明问题不在"紧张"（随机性），而在更深层的地方。

对 AI 来说，即使每个单独的回应都是确定的，多轮对话中的微小差异也会像蝴蝶效应一样被放大，最终导致完全不同的结果。

## 推理模型的表现

研究测试了两个"推理模型"：OpenAI 的 o3 和 Deepseek 的 R1。结果发现：

- 额外的测试时计算（推理 tokens）并不能帮助模型处理多轮未完全指定的对话，这两个推理模型的退化方式与非推理模型相似
- 推理模型往往生成更长的回应（平均比非推理模型长 33%）
- 而研究已经证明冗长的回应与较差的性能相关

这个发现特别令人意外。推理模型就像是给学生更多的思考时间，让他们在草稿纸上多算几遍再给答案。

在单独的题目上，这确实有用。

但在多轮对话中，即使给了更多思考时间，学生还是会在信息不完整的情况下做错误假设，而且一旦做了错误假设，更多的思考时间可能反而让他更坚持错误的方向。

## 缓解策略测试

研究团队测试了两种可能的缓解策略：

### RECAP 策略

在 SHARDED 对话结束后，增加一个最终轮次，重新陈述所有之前的用户指令。

### SNOWBALL 策略

在每一轮都重复之前的所有信息，形成"滚雪球"效应。

实验结果：

- 两种策略都显示出一定的改善效果
- RECAP 策略表现稍好，但它在实际应用中不太现实（因为无法预知对话何时结束）
- SNOWBALL 策略能够减少 15-20%的性能下降
- 但都无法完全恢复到单轮对话的水平

这就像一个健忘的朋友，你每次都要提醒他之前说过什么。

RECAP 策略就像在聊天结束时总结一遍"我们刚才说了什么什么...";

SNOWBALL 策略就像每句话都要重复一遍之前的内容"还记得我刚才说的 A 吗？现在加上 B，还有刚才的 A..."

虽然这样做有帮助，但显然不是理想的交流方式。

## 翻译任务的特殊发现

研究团队还测试了一个翻译任务（基于 WMT 2019 德语到英语的文档级翻译），结果出现了有趣的例外：

- 在翻译任务中，模型在 SHARDED 设置中的性能没有显著下降
- BLEU 分数在所有设置中都保持在 10%的差异范围内
- 研究者认为这是因为翻译任务本质上是"episodic"的——可以逐句处理而不需要复杂的信息整合

翻译任务就像搭积木，每一句话相对独立，第三句话的翻译不太依赖第一句话的内容。

而数学题或编程任务更像是解谜，每个信息片段都可能影响整体解决方案。

这个发现很重要，因为它说明问题不是多轮对话本身，而是特定类型的认知任务与多轮交互之间的冲突。

## 对用户的实用建议

论文为用户提供了两个重要的实用建议：

### 必要时重新开始（If time allows， try again）

如果对话没有达到预期效果，重新开始一个包含相同信息的新对话，可能比继续当前对话获得更好的结果。这是因为当前 LLMs 可以在对话中迷失，我们的实验表明在对话中坚持是无效的。

这就像电脑卡住了重启一样。

很多有经验的 AI 用户已经本能地这样做了：当发现对话"跑偏"时，他们会果断开始新的对话。

这虽然有点麻烦，但往往比试图"挽救"当前对话更有效。

### 先整合再重试（Consolidate before retrying）

由于 LLMs 在处理分散在多个轮次中的信息时效果不佳，将指令要求整合到单个指令中是提高模型能力和可靠性的有效策略。

当用户注意到模型在对话中迷失时，他们可以要求 LLM："请整合我到目前为止告诉你的所有内容"，然后将回应带到新对话中。

## 