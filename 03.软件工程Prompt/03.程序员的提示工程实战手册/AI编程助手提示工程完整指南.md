# AI编程助手提示工程完整指南

> **原文链接**: https://mp.weixin.qq.com/s/9BNpGcbLYG7BPQ1z-3iSZA  
> **作者**: Addy Osmani  
> **翻译**: 宝玉AI  
> **发布时间**: 2025年06月25日

## 目录

- [引言](#引言)
- [核心提示技巧](#核心提示技巧)
- [高效代码提示的基础](#高效代码提示的基础)
- [调试代码的提示模式](#调试代码的提示模式)
- [重构和优化的提示模式](#重构和优化的提示模式)
- [实现新功能的提示模式](#实现新功能的提示模式)
- [常见的提示反模式及其避免方法](#常见的提示反模式及其避免方法)
- [结论](#结论)

## 引言

开发者们正日益依赖 AI 编程助手来加速日常工作流程。这些工具可以自动补全函数、建议错误修复，甚至能生成整个模块或最小可行产品（MVP）。然而，我们中许多人已经体会到，**AI 输出的质量在很大程度上取决于你提供的提示词质量**。

换言之，**提示工程已成为一项必备技能**。一句措辞不当的请求可能只会得到无关或泛泛的答案，而一个精心设计的提示则能产出深思熟虑、准确甚至富有创意的代码解决方案。

本文将从实践角度出发，探讨如何系统地为常见的开发任务构建有效的提示。

### 核心理念

AI 结对程序员虽然强大，但并非无所不能——**除了你告知或作为上下文包含的信息外，它们对你的具体项目或意图一无所知**。你提供的信息越多，输出的效果就越好。

## 核心提示技巧

| 技巧 | 提示词模板 | 目的 |
|------|------------|------|
| **1. 角色提示** | "你是一位资深的 {某种语言} 开发者。请为了 {某个目标} 来审查这个函数。" | 模拟专家级的代码审查、调试或重构，获得更高质量的建议 |
| **2. 明确上下文** | "问题是：{问题摘要}。代码如下。它本应 {预期行为}，但现在却 {实际行为}。这是为什么？" | 清晰地框定问题，避免 AI 给出泛泛而谈、流于表面的回答 |
| **3. 输入/输出示例** | "当输入为 {某个输入} 时，这个函数应该返回 {预期输出}。你能编写或修复这段代码吗？" | 通过具体示例来展示你的意图，引导 AI 智能体准确理解需求 |
| **4. 迭代式链条** | "第一步，生成组件的骨架代码。下一步，我们来添加状态管理。最后，处理 API 调用。" | 将复杂的大任务分解成连续的小步骤，避免因提示过于庞大或含糊而导致 AI 混乱 |
| **5. 模拟调试** | "请逐行过一遍这个函数。每个变量的值是什么？代码最有可能在哪里出错？" | 让 AI 智能体模拟代码的运行时行为，从而帮助你发现那些隐藏很深的 bug |
| **6. 功能蓝图** | "我正在构建 {某个功能}。需求是：{几点核心需求}。技术栈是：{所用技术}。请搭建出初始组件的脚手架，并解释你的选择。" | 借助 AI 主导的规划和脚手架能力，高效启动新功能的开发 |
| **7. 重构指导** | "请重构这段代码以提升 {某个目标}，例如 {可读性、性能、代码风格等}。请用注释来解释你做了哪些更改。" | 确保 AI 的代码重构与你的核心目标对齐，而不是进行随意的、不必要的修改 |
| **8. 寻求替代方案** | "你能用函数式风格重写这段代码吗？如果用递归版本会是什么样子？" | 探索多种不同的实现路径，开阔思路，并丰富你的技术工具箱 |
| **9. 小黄鸭调试法** | "我是这样理解这个函数功能的：{你的解释}。我有什么遗漏吗？这个解释能暴露出什么 bug 吗？" | 让 AI 扮演"小黄鸭"的角色，通过向它解释来挑战你自己的理解，并发现逻辑上的矛盾之处 |
| **10. 约束锚定** | "请避免使用 {例如：递归}，并严格遵守 {例如：ES6 语法，不使用外部库}。请为 {例如：内存占用} 进行优化。函数如下：" | 给 AI 设定明确的边界和限制，防止它"自由发挥"过度，或引入与项目不兼容的代码模式 |

## 高效代码提示的基础

向 AI 编码工具提问，有点像与一个极其刻板、但有时知识渊博的合作者沟通。为了得到有用的结果，你需要清晰地设定场景，并引导 AI 明白你想要什么以及你希望它如何做。

### 基础原则

#### 1. 提供丰富的上下文

始终假设 AI 对你的项目一无所知，除了你提供的信息。包括相关细节，如：
- 编程语言、框架和库
- 具体的函数或代码片段
- 确切的错误信息
- 代码应该做什么的描述

**示例**：
```
我有一个使用 Express 和 Mongoose 的 Node.js 函数，它应该通过 ID 获取用户，
但抛出了一个 TypeError。这是代码和错误信息……
```

#### 2. 明确你的目标或问题

模糊的查询导致模糊的答案。与其问"我的代码为什么不工作？"，不如精确指出你需要什么洞见。

**调试提示公式**：
```
它预期会[预期行为]，但在给定[示例输入]时，它却[当前行为]。错误在哪里？
```

#### 3. 分解复杂任务

在实现新功能或处理多步骤问题时，不要把整个问题塞进一个巨大的提示里。将工作分成更小的部分并进行迭代通常更有效。

**示例**：
```
首先，为产品列表页面生成一个 React 组件的骨架。
接下来，我们添加状态管理。
然后，我们再集成 API 调用。
```

#### 4. 包含输入/输出示例或预期行为

如果你能用一个例子来说明你想要什么，就这么做。

**示例**：
```
给定数组 [3,1,4]，这个函数应该返回 [1,3,4]。
```

#### 5. 利用角色或身份

要求 AI"扮演"某个特定角色或身份可以影响答案的风格和深度。

**示例**：
```
扮演一位资深的 React 开发者，审查我的代码中可能存在的错误
```

#### 6. 迭代并优化对话

提示工程是一个互动过程，而非一蹴而就。开发者通常需要审查 AI 的第一个回答，然后提出后续问题或进行修正。

#### 7. 保持代码的清晰和一致性

编写清晰、结构良好的代码和注释。有意义的函数和变量名、一致的格式以及文档字符串不仅使你的代码对人类来说更容易理解，也为 AI 提供了更强的线索。

## 调试代码的提示模式

调试是 AI 助手的天然应用场景。这就像拥有一个不仅会倾听，还会用建议回应你的"小黄鸭"。

### 调试策略

#### 1. 清晰描述问题和症状

在提示的开头，描述哪里出了问题以及代码应该做什么。始终包含确切的错误信息或不正确的行为。

**❌ 糟糕的提示**：
```
我的代码不工作
```

**✅ 改进的提示**：
```
我有一个 JavaScript 函数，它应该计算一个数字数组的总和，但它返回的是 NaN (Not a Number)，
而不是实际的总和。这是代码：[包含代码]。对于像 [1,2,3] 这样的数字数组，
它应该输出一个数字（总和），但我得到了 NaN。这个错误的原因可能是什么？
```

#### 2. 对棘手的错误使用分步或逐行的方法

对于更复杂的逻辑错误，你可以提示 AI 逐行执行代码。

**示例**：
```
逐行执行这个函数，并跟踪每一步 total 变量的值。
它没有正确累加——逻辑错在哪里了？
```

#### 3. 尽可能提供最小可复现示例

如果你能提取或简化代码并仍然能复现问题，就这么做，并把它提供给 AI。

#### 4. 提出集中的问题和后续追问

提供上下文后，直接询问你需要什么通常很有效。

**示例**：
```
可能是什么原因导致了这个问题，我该如何修复？
```

### 调试示例：糟糕的提示 vs. 改进的提示

假设我们有一个简单的 Node.js 函数，旨在将用户对象列表转换为按用户 ID 查找的映射表。然而，它抛出了一个错误。

**有问题的代码**：
```javascript
// 有问题的函数：将用户数组转换为按 ID 索引的 map
function mapUsersById(users) {
  const userMap = {};
  for (let i = 0; i <= users.length; i++) {
    // 注意：使用 <= 会在最后一次迭代中越界
    const user = users[i];
    userMap[user.id] = user;
  }
  return userMap;
}

// 示例用法：
const result = mapUsersById([{ id: 1, name: "Alice" }]);
```

**❌ 糟糕的提示**：
```
为什么我的 mapUsersById 函数不工作？
```

**AI 对糟糕提示的回应**：
```
没有更多信息很难说。你确定函数是用一个合法的用户数组调用的吗？
也许问题在于 users 是空的或者不是一个数组，导致循环没有正确执行。
也可能是设置 userMap 属性时出错。请确保每个用户都有一个 id 属性。
如果 users 是 undefined 或 null，也会导致它不工作。
```

**✅ 改进的提示**：
```
我有一个 JavaScript 函数 mapUsersById，它应该将一个用户对象数组转换为一个以用户 ID 为键的 map（对象）。
然而，当我运行它时，它抛出了一个错误。例如，当我传入 [ {id: 1, name: "Alice"} ] 时，
我得到 TypeError: Cannot read property 'id' of undefined。

这是函数代码：
function mapUsersById(users) {
  const userMap = {};
  for (let i = 0; i <= users.length; i++) {
    const user = users[i];
    userMap[user.id] = user;
  }
  return userMap;
}

它应该返回 { "1": {id: 1, name: "Alice"} }。错误是什么，我该如何修复它？
```

**AI 对改进提示的回应**：
```
错误 Cannot read property 'id' of undefined 表明在某个时刻，user 是 undefined，
然后你试图访问 user.id。查看你的循环，问题出在循环边界上。你使用了 i <= users.length。

如果 users.length 是 1（数组中有一个用户），循环会以 i = 0 和 i = 1 运行。
当 i 是 1 时，users[1] 是 undefined（因为数组中唯一的索引是 0）。
因此，在那次迭代中 user 变为 undefined，而 user.id 抛出了一个 TypeError。

修复方法是将循环条件改为 i < users.length，这样 i 的范围就是从 0 到 users.length-1。

例如：
for (let i = 0; i < users.length; i++) {
  const user = users[i];
  userMap[user.id] = user;
}

这样，对于第一个（也是唯一一个）元素，i 将是 0，然后循环停止。
做了这个改动后，你的函数将不再遇到 undefined 的用户，
并且应该能按预期工作，对于给定的输入返回 {"1": {id: 1, name: "Alice"}}。
```

### 额外的调试策略

- **询问潜在原因**：如果你真的束手无策，可以稍微扩大问题范围
- **"向小黄鸭提问"**：向 AI 解释你的代码，让它验证或批评你的理解
- **让 AI 创建测试用例**：询问可能让函数崩溃的边缘情况
- **角色扮演代码审查员**：让 AI 扮演细致的审查员角色

## 重构和优化的提示模式

重构代码——在不改变其功能的前提下，使其更清晰、更快或更符合语言习惯——是 AI 助手可以大放异彩的领域。

### 重构策略

#### 1. 明确陈述你的重构目标

单独的"重构这段代码"太开放了。你需要明确目标：

**示例**：
```
重构以下函数以提高其可读性和可维护性（减少重复，使用更清晰的变量名）。
```

或者：
```
优化这个算法的速度——它在处理大输入时太慢了。
```

#### 2. 提供必要的代码上下文

在重构时，要包含：
- 需要改进的完整函数或部分
- 相关的周围上下文
- 语言和框架信息
- 版本或环境细节

#### 3. 鼓励在提供代码的同时附上解释

**示例**：
```
请建议一个重构版本的代码，并解释你所做的改进。
```

#### 4. 使用角色扮演来设定高标准

**示例**：
```
扮演一位经验丰富的 TypeScript 专家，重构这段代码以符合最佳实践和现代标准。
```

### 重构示例：糟糕的提示 vs. 改进的提示

假设我们有一个函数，它进行两次数据库调用并做一些处理。它能工作，但不够美观——有重复的代码，而且难以阅读。

**原始代码**：
```javascript
// 原始函数：获取两个列表并处理它们（需要重构）
async function getCombinedData(apiClient) {
  // 获取用户列表
  const usersResponse = await apiClient.fetch('/users');
  if (!usersResponse.ok) {
    throw new Error('Failed to fetch users');
  }
  const users = await usersResponse.json();
  
  // 获取订单列表
  const ordersResponse = await apiClient.fetch('/orders');
  if (!ordersResponse.ok) {
    throw new Error('Failed to fetch orders');
  }
  const orders = await ordersResponse.json();
  
  // 合并数据（将用户与订单匹配）
  const result = [];
  for (let user of users) {
    const userOrders = orders.filter(o => o.userId === user.id);
    result.push({ user, orders: userOrders });
  }
  return result;
}
```

**❌ 提示 1 (不具体)**：
```
重构上面的 getCombinedData 函数。
```

**✅ 提示 2 (目标导向)**：
```
重构上面的 getCombinedData 函数以消除重复代码并提高性能。具体来说：
(1) 避免重复用户和订单的获取逻辑——也许使用一个辅助函数或将它们一起获取。
(2) 如果可能，并行获取两个列表。
(3) 保留每次获取的错误处理（我们想知道是哪个调用失败了）。
(4) 改进数据合并，可能通过使用更高效的查找结构而不是嵌套循环。
提供重构后的代码，并附上解释更改的注释。
```

### 额外的重构技巧

- **分步重构**：如果代码非常大，一次只处理一个改进
- **询问替代方法**：探索不同的实现角度
- **结合重构与解释来学习模式**：把 AI 当作学习工具
- **验证和测试**：在任何 AI 生成的重构之后，一定要运行测试

## 现代调试场景

### React Hook 依赖问题

**❌ 糟糕的提示**：
```
我的 useEffect 不正常
```

**✅ 增强的提示**：
```javascript
// 我有一个 React 组件，它获取用户数据，但导致了无限次的重新渲染。这是我的代码：
const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUser(userId).then(setUser).finally(() => setLoading(false));
  }, [userId, setUser, setLoading]); // 问题在这里

  return loading ? <div>Loading...</div> : <div>{user?.name}</div>;
};

预期行为： 当 userId 改变时，应该只获取一次用户数据。
实际行为： 组件无限次地重新渲染。
控制台错误： "Warning: Maximum update depth exceeded"
是什么导致了这个无限循环，我该如何修复依赖数组？
```

**为什么这样有效**：提供了确切的代码、错误信息、预期与实际行为的对比，并专注于一个通常被误解的特定 React 模式。

### 现代状态架构

**❌ 糟糕的提示**：
```
为我的 Next.js 电商应用构建状态管理
```

**✅ 增强的提示**：
```
我正在构建一个 Next.js 14 的电商应用，需要设计状态管理架构。以下是我的需求：

组件：
• 产品列表页 (需要: products[], filters, pagination)
• 购物车 (需要: cart items, totals, shipping info)
• 用户认证 (需要: user profile, auth status, preferences)
• 实时通知 (需要: toast messages, error states)

技术限制：
• Next.js 14，使用 App Router 和 Server Components
• TypeScript 严格模式
• 为 SEO 进行服务器端数据获取
• 购物车/用户操作的客户端交互性
• 状态应在导航之间持久化

我应该使用：
1. 为每个领域（购物车、认证、通知）使用 Zustand stores
2. React Query/TanStack Query 用于服务器状态 + Zustand 用于客户端状态
3. 一个带有切片（slices）的单一 Zustand store

请提供一个推荐的架构，并附上代码示例，展示如何构建 stores 并与 Next.js App Router 模式集成。
```

**为什么这样有效**：这是一个真实世界的场景，具有特定的技术栈、清晰的需求，并要求提供包含实现细节的架构指导。

## 实现新功能的提示模式

AI 代码助手最令人兴奋的用途之一是帮助你从头开始编写新代码或将新功能集成到现有代码库中。这里的挑战通常是这些任务是开放式的——实现一个功能有很多种方法。

### 功能实现策略

#### 1. 从高层指令开始，然后逐步深入

首先用通俗的语言概述你想要构建什么，可能将其分解成更小的任务。

**示例**：
```
概述一个计划，在我的 React 应用中添加一个搜索功能，
该功能可以按名称过滤产品列表。产品是从一个 API 获取的。
```

AI 可能会给你一个分步计划：
1. 添加一个用于搜索查询的输入字段
2. 添加状态来保存查询
3. 根据查询过滤产品列表
4. 确保搜索不区分大小写

然后你可以用集中的提示来处理每个要点：
```
好的，实现第 1 步：创建一个 SearchBar 组件，
带有一个可以更新 searchQuery 状态的输入框。
```

#### 2. 提供相关上下文或参考代码

如果你要向现有项目添加功能，向 AI 展示该项目中类似功能是如何完成的会非常有帮助。

**示例**：
```
这是一个现有的 UserList 组件（代码…）。
现在创建一个类似的 ProductList 组件，但要包括一个搜索栏。
```

#### 3. 使用注释和 TODO 作为内联提示

当直接在带有 Copilot 的 IDE 中工作时，一个有效的工作流程是编写一条描述你需要的下一块代码的注释。

**示例**：
```javascript
// TODO: 验证请求负载（确保提供了 name 和 email）
```

#### 4. 提供预期输入/输出或用法的示例

**示例**：
```
用 JavaScript 实现一个函数 formatPrice(amount)，
它接收一个数字（如 2.5）并返回一个格式化为美元的字符串（如 '$2.50'）。
```

#### 5. 当结果不符合你的期望时，用更多细节或约束重写提示

生成新功能的第一次尝试没有成功是很常见的。把 AI 当作一个提交了初稿的初级开发者——现在你需要给出反馈。

**示例**：
```
这个解决方案能用，但我更希望你使用内置的数组 filter 方法而不是 for 循环。
```

### 功能实现示例：在 AI 辅助下构建 React 组件

假设我们有一个 React 应用，我们想添加一个新组件：一个带有搜索过滤器和排序下拉菜单的产品列表。

**初始提示（功能请求）**：
```
创建一个名为 ProductList 的 React 函数式组件，用于显示产品列表，
并包含一个文本输入框以按名称过滤产品。该组件应：

从 /api/products 获取产品数组（假设它返回 {id, name, ...} 的 JSON 数组），
将其存储在状态中，并允许用户在搜索框中键入以按名称过滤显示的产品（不区分大小写匹配）。
在一个简单的 <ul> 中显示过滤后的列表，并显示产品名称。
包含基本的错误处理（例如，如果 API 调用失败，则显示错误消息）和获取数据时的加载状态。
```

**AI 可能的回应**：
```javascript
import React, { useState, useEffect } from 'react';

function ProductList() {
  const [products, setProducts] = useState([]);
  const [search, setSearch] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchProducts() {
      try {
        const res = await fetch('/api/products');
        if (!res.ok) throw new Error('Network response was not ok');
        const data = await res.json();
        setProducts(data);
      } catch (err) {
        console.error('Failed to fetch products:', err);
        setError('Failed to load products.');
      } finally {
        setLoading(false);
      }
    }
    fetchProducts();
  }, []);

  const handleSearchChange = (e) => {
    setSearch(e.target.value);
  };

  // 按搜索查询过滤产品（不区分大小写）
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(search.toLowerCase())
  );

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>{error}</div>;

  return (
    <div className="product-list">
      <input
        type="text"
        placeholder="Search products"
        value={search}
        onChange={handleSearchChange}
      />
      <ul>
        {filteredProducts.map(prod => (
          <li key={prod.id}>{prod.name}</li>
        ))}
      </ul>
    </div>
  );
}

export default ProductList;
```

### 功能实现的额外技巧

- **让 AI 搭建骨架，然后你填充具体内容**：有时让 AI 生成一个粗略的结构，然后你再进行微调会很有用
- **要求处理边缘情况**：询问应该考虑哪些边缘情况
- **文档驱动开发**：先编写文档字符串或用法示例，然后让 AI 实现函数以匹配

## 常见的提示反模式及其避免方法

并非所有提示都是平等的。认识到反模式——那些导致 AI 响应不佳的常见错误——同样具有启发性。

### 主要反模式

#### 1. 模糊的提示

**问题**：典型的"它不工作，请修复它"或"写一个做 X 的东西"而没有足够细节。

**解决方法**：添加上下文和具体细节。如果你发现自己提出的问题得到的回答像是一个魔力八号球的响应，停下来，用更多细节重新组织你的查询。

**检验方法**：读一下你的提示，然后问自己，"这个问题能适用于几十种不同的场景吗？"如果是，那它就太模糊了。

#### 2. 超载的提示

**问题**：要求 AI 一次做太多的事情。

**示例**：
```
生成一个完整的 Node.js 应用，包括身份验证、React 前端和部署脚本。
```

**解决方法**：拆分任务。排定优先级：一次只做一件事。

#### 3. 缺失问题

**问题**：提供大量信息，但从未清楚地提出问题或指明需要什么。

**示例**：
```
这是我的代码 [大段代码]
```

**解决方法**：始终包含一个明确的要求，例如"识别上述代码中的任何错误"、"解释这段代码是做什么的"。

#### 4. 模糊的成功标准

**问题**：要求优化或改进，但没有定义成功是什么样子。

**示例**：
```
让这个函数更快。
```

**解决方法**：量化或限定改进。

**改进示例**：
```
优化这个函数，使其以线性时间运行（当前版本是二次的）
```

#### 5. 忽略 AI 的澄清或输出

**问题**：当 AI 提出澄清问题时，忽略这些问题只是重申请求。

**解决方法**：回答 AI 的问题或完善你的提示以包含这些细节。

#### 6. 风格多变或不一致

**问题**：在指令中在第一人称和第三人称之间切换，或者以一种令人困惑的方式混合伪代码和实际代码。

**解决方法**：在单个提示中保持一致的风格。

#### 7. 模糊的引用

**问题**：在使用聊天时，如果你说"上面的函数"或"之前的输出"，要确保引用是清晰的。

**解决方法**：再次引用代码或具体指明你想要重构的函数。

### 重写提示的战术方法

当事情出错时：

1. **识别 AI 响应中缺失或不正确的地方**
2. **在一个新的提示中添加或强调那个要求**
3. **如果需要，进一步分解请求**
4. **如果对话卡住了，考虑重新开始**

## 结论

提示工程既是一门艺术，也是一门科学——正如我们所见，它正迅速成为使用 AI 代码助手的开发者必备的技能。通过精心设计清晰、富含上下文的提示，你实际上是在教 AI 你需要什么，就像你引导一个新的人类团队成员或向同事解释一个问题一样。

### 关键收获

- **我们学会了向 AI 提供与向同事求助时相同的信息**：代码应该做什么、它如何行为异常、相关的代码片段等等——从而获得更有针对性的帮助

- **我们看到了与 AI 迭代的威力**，无论是逐行遍历函数逻辑，还是通过多个提示优化解决方案。耐心和迭代将 AI 从一个一次性的代码生成器转变为真正的结对程序员

- **我们利用角色扮演和身份设定来提升响应的水平**——将 AI 视为代码审查员、导师或某个技术栈的专家。这通常会产生更严谨、解释更丰富的输出

- **对于重构和优化，我们强调了定义"好"的标准**（无论是更快、更清晰、更符合语言习惯等），而 AI 也表明，在引导下，它可以应用已知的最佳实践

- **我们还演示了在 AI 辅助下逐步构建新功能**，表明即使是复杂的任务也可以分解并一次一个提示地解决

- **在此过程中，我们识别了要避免的陷阱**：保持提示既不太模糊也不太超载，始终明确我们的意图和约束，并在 AI 输出不准确时随时准备调整

### 最佳实践总结

1. **提供丰富的上下文** - 假设 AI 对你的项目一无所知
2. **明确你的目标** - 避免模糊的查询
3. **分解复杂任务** - 迭代式开发
4. **包含具体示例** - 输入/输出示例澄清需求
5. **利用角色扮演** - 设定专家身份获得更好的建议
6. **保持迭代对话** - 提示工程是一个互动过程
7. **维护代码质量** - 清晰的代码为 AI 提供更好的线索

### 未来展望

当你将这些技巧融入你的工作流程时，你可能会发现与 AI 的合作变得更加直观。你会培养出一种感觉，知道什么样的措辞能得到最好的结果，以及在模型偏离轨道时如何引导它。

请记住，AI 是其训练数据的产物——它看过许多代码和解决问题的例子，但提供方向，指明哪些例子在当下是相关的，这正是你的工作。**本质上，你设定上下文，AI 跟进执行**。

同样值得注意的是，**提示工程是一种不断发展的实践**。开发者社区在不断发现新的技巧——一个巧妙的一行提示或一个结构化模板可能会突然在社交媒体上走红，因为它解锁了人们之前没有意识到的能力。

总而言之，**提示工程使开发者能够从 AI 助手中获得更多**。这是令人沮丧的体验（"这个工具没用，它给了我一堆胡说八道"）和富有成效的体验（"这感觉就像和一个为我写样板代码的专家结对编程"）之间的区别。

通过应用我们所涵盖的策略手册——从提供详尽的上下文到微调 AI 的风格和思维——你可以将这些专注于代码的 AI 工具变成你开发工作流程的真正延伸。最终结果不仅是你编码更快，而且你常常会在这个过程中学到新的见解和模式，从而提升你自己的技能水平。

作为最后的总结，请记住**提示是一个迭代的对话**。用你与另一位工程师沟通时所使用的同样清晰、耐心和彻底的态度来对待它。这样做，你会发现 AI 助手可以显著放大你的能力——帮助你更快地调试、更聪明地重构，并更轻松地实现功能。

**祝你提示愉快，编码快乐！**

---

> **原文**：《The Prompt Engineering Playbook for Programmers》
> **链接**：https://addyo.substack.com/p/the-prompt-engineering-playbook-for
