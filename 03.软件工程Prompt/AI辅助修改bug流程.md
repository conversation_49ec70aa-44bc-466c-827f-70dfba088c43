# AI辅助开发指南

## 🎯 三步简洁协作流程

### 核心理念
解决AI在大型项目中上下文信息不足的问题，通过结构化的三步流程提升开发效率和代码质量。

### 三步流程

#### 第一步：定位问题
```
[功能名称]出现了[具体bug描述]，请帮我找到相关代码并分析问题。
```

#### 第二步：理解逻辑  
```
请分析这个问题的产生的原因和影响范围。
```

#### 第三步：提供方案
```
请提供修复方案并实现代码。
```




## 📊 复杂问题的文档化处理

### 技术文档
```
请为[功能名称]编写技术分析文档，包括：
- 现状分析
- 问题根因  
- 解决思路
- 实施步骤
```

### 何时使用文档化处理

**建议使用文档/图表：**
- 涉及多个模块的复杂功能
- 性能优化问题
- 架构重构需求
- 新功能设计
- 难以定位的疑难bug

**直接处理：**
- 单一模块的简单bug
- UI显示问题
- 数据格式问题
- 简单的功能增加

## 🔧 图表类型选择指南

### 🕐 时序图 (Sequence Diagram)
**关键词：** 交互、调用、通信、异步、回调

**适用场景：**
- 网络请求流程 - API调用 → 数据解析 → UI更新
- 组件间通信 - Controller ↔ View ↔ Model 的交互
- 用户操作链 - 点击 → 网络请求 → 页面跳转 → 数据传递
- 异步处理 - 多线程操作、队列处理、回调执行
- 第三方集成 - SDK调用、支付流程、分享流程

**典型问题：**
- "用户点击收藏后，数据没有及时更新"
- "网络请求返回后，界面刷新有延迟"  
- "直播间礼物动画和音效不同步"

**Prompt：**
```
请画一个[功能名称]的时序图，展示各组件的交互过程。
```

### 📋 流程图 (Flowchart)
**关键词：** 步骤、流程、逻辑、判断、分支

**适用场景：**
- 业务流程 - 用户注册、登录、购买流程
- 算法逻辑 - 数据排序、筛选、计算规则
- 决策分支 - 权限判断、条件处理、异常处理
- 操作指南 - 功能使用步骤、配置流程

**典型问题：**
- "用户登录流程复杂，容易出错"
- "股票筛选算法逻辑不清晰"
- "权限判断有漏洞"

**Prompt：**
```
请画一个[功能名称]的业务流程图。
```

### 🔄 状态图 (State Diagram)  
**关键词：** 状态、切换、变化、生命周期

**适用场景：**
- UI状态管理 - 加载中、成功、失败、空数据、网络错误
- 播放器状态 - 播放、暂停、缓冲、停止、错误
- 连接状态 - 已连接、断开、重连中、连接失败
- 用户状态 - 未登录、已登录、会员、过期
- 页面生命周期 - 创建、显示、隐藏、销毁

**典型问题：**
- "页面加载状态显示混乱"
- "视频播放器状态切换有bug"
- "网络断开重连逻辑有问题"

**Prompt：**
```
请画一个[功能名称]的状态转换图。
```

### 🏗️ 架构图 (Architecture Diagram)
**关键词：** 结构、依赖、模块、层次、整体

**适用场景：**
- 模块关系 - 各业务模块间的依赖关系
- 系统架构 - MVC/MVVM架构、网络层架构
- 数据流向 - 数据在各层间的流转
- 组件层次 - UI组件的嵌套关系
- 技术栈 - 使用的框架、库、工具的关系

**典型问题：**
- "代码耦合度太高，难以维护"
- "不清楚模块间的依赖关系"
- "新功能不知道放在哪个模块"

**Prompt：**
```
请画一个[功能名称]的架构图，展示模块间的依赖关系。
```

## 🎯 快速判断法则

### 问自己这些问题：

1. **关注的是"谁和谁交互"？** → **时序图**
   - 涉及多个对象/组件的协作
   - 有明确的调用顺序和时间关系

2. **关注的是"怎么做"？** → **流程图**  
   - 有明确的步骤和分支判断
   - 需要梳理操作或业务逻辑

3. **关注的是"状态如何变化"？** → **状态图**
   - 对象有多种状态
   - 状态间有明确的转换条件

4. **关注的是"整体结构"？** → **架构图**
   - 需要了解模块关系
   - 涉及系统设计或重构

## 💡 实际应用示例

### 简单Bug修复
```
用户：股票列表页面滚动时会崩溃，请帮我找到相关代码并分析问题。
AI：[自动查找代码并分析]

用户：请分析这个问题的原因和影响范围。
AI：[分析问题]

用户：请提供修复方案并实现代码。
AI：[提供解决方案]
```

### 复杂功能问题
```
用户：直播间的礼物动画功能有性能问题，请先为这个功能编写技术分析文档。
AI：[生成技术文档]

用户：请画一个礼物动画功能的时序图。
AI：[生成时序图]

用户：基于文档和图表，请提供性能优化方案。
AI：[提供优化方案]
```

### 场景示例

**场景1：直播间礼物功能bug**
- 问题：礼物动画卡顿，音效延迟
- 选择：时序图
- 原因：涉及UI动画、音频播放、网络同步的交互时序

**场景2：用户注册流程优化**
- 问题：注册步骤复杂，用户容易放弃
- 选择：流程图  
- 原因：需要梳理注册的各个步骤和判断分支

**场景3：视频播放器异常**
- 问题：播放状态显示错误，控制按钮失效
- 选择：状态图
- 原因：播放器有多种状态，需要理清状态转换逻辑

**场景4：新模块设计**
- 问题：不确定新功能应该如何融入现有架构
- 选择：架构图
- 原因：需要了解现有模块结构和依赖关系

## 📝 组合使用建议

复杂问题可以组合使用多种图表：

1. **架构图** → 了解整体结构
2. **流程图** → 梳理业务逻辑  
3. **时序图** → 分析具体交互
4. **状态图** → 管理状态变化

这样可以从宏观到微观，全面理解和解决问题。

## ✅ 最佳实践

- **简洁优先** - 用最简单的prompt描述问题
- **复杂问题先文档化** - 生成技术文档和图表后再编码
- **验证理解** - 每步完成后确认AI理解正确
- **保持项目一致性** - 遵循现有架构和代码规范
