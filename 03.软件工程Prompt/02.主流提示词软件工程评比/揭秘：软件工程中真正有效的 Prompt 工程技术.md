# 揭秘：软件工程中真正有效的 Prompt 工程技术

原文链接: https://mp.weixin.qq.com/s/Q8aVU4fJ1rui5zOuUlQX5A
原创 AI修猫Prompt AI修猫Prompt 2025年06月11日 00:03 北京
现在市面上有46种 Prompt 工程技术（论文中引用的数据，和我去年这个时候介绍的论文是一致的《防骗| 连这些引用量最高的核心Prompt都不知道，还敢打着专家大师旗号蒙人》），但真正能在软件工程任务中发挥作用的，可能只有那么几种。来自巴西联邦大学、加州大学尔湾分校等顶级院校的研究者们，花了大量时间和计算资源，调研了58种，整理了46种，最终筛选测试了14种主流提示技术在10个软件工程任务上的表现，用了4个不同的大模型（包括咱们的Deepseek-V3），总共跑了2000多次实验。结果发现，**ES-KNN**（示例选择K近邻）、**USC**（通用自一致性）和**ToT**（线程思维）这几种技术确实比其他方法强不少，但关键是要看您在做什么任务。
而且万万没想到，最牛的Prompt技巧，竟然是“装大神”（**Role Prompting**）和“当复读机”（**Exemplar Selection**）

---

## 这14种技术到底是什么？

研究者从46种提示工程技术中精选出了14种进行深度测试，涵盖了**零样本、少样本、思维生成、集成、自我批评和分解**等六个核心维度。让我们来看看这些技术各自的特色和优劣：

1. **ES-KNN：找最像的例子给模型看**

    ES-KNN（**示例选择K近邻**）的核心思路其实挺简单的，就是给您的代码找几个最相似的例子，然后一起丢给大模型。具体怎么做呢？研究者先把所有代码样本转换成向量（用的是专门训练过的多语言代码嵌入模型），然后计算余弦相似度，找出top-k个最相似的样本作为示例（Few-shot）。这就像您在写代码时会参考类似的代码片段一样，给模型提供参考确实能提高准确率。

2. **CCoT：正反例子一起学**

    少样本对比思维链（Few Shot Contrastive CoT）是个挺聪明的方法，它不仅给模型看正确的推理步骤，还会展示错误的思路，让模型学会区分对错。用过Midjourney的朋友对这个方法应该熟悉。在代码调试任务中，CCoT会同时展示正确的调试思路和常见的错误分析，帮助模型避开坑。

3. **TroT：三分支探索策略**

    树形思维（Tree Of Thought）通过构建多个分支推理路径来探索不同的解决策略。**注意别和下面的ToT搞混了！**TroT主要用于复杂设计问题，会让模型同时考虑多种可能的解决方案，但研究数据显示它在软件工程任务中表现并不理想。

4. **SA：让模型自问自答**

    自我提问（Self Ask）技术会让模型在回答问题前先生成自己的后续问题，帮助它将复杂问题分解成小步骤。比如在代码分析任务中，模型可能会先问自己“这个函数的主要功能是什么？”“可能存在什么边界情况？”然后再给出最终答案。

5. **USC：让模型自己投票选最好答案**

    通用自一致性（Universal Self Consistency）这个技术更有意思，它会让模型对同一个问题回答多次，然后用一个“元提示”来选择最一致或最可靠的答案。比如您让模型生成代码翻译，它可能给出5个不同版本，然后USC会分析这些答案的一致性，选出最靠谱的那个。研究数据显示，这种方法在**代码问答**和**代码生成**任务中表现特别出色，错误率明显降低。

6. **SR：理想很丰满，现实很骨感**

    自我优化（Self Refine）听起来很高级，让模型不断自我评估和改进答案，但实验结果显示它经常是最差的那一批。研究者发现，在多个任务中SR都排在倒数几名，特别是在**代码理解**任务中表现糟糕。可能是因为模型在自我评估时容易陷入错误的反馈循环，越改越错。

7. **SG-ICL：自动生成学习样例**

    自生成上下文学习（Self-Generated In-Context Learning）能够自动生成上下文示例来模拟少样本学习，简化了编程任务的提示构建过程。这个技术在**Bug修复**任务中表现不错，但在其他任务中经常排在最差行列。

8. **ToT：像决策树一样思考问题**

    线程思维（Thread Of Thought）技术会引导大模型逐步解决问题，专注于将大型或复杂任务分解成较小的可管理部分。在**缺陷检测**任务中，ToT会引导模型逐个检查代码组件，而不是一次性分析整个代码块。数据表明，这种方法在需要复杂逻辑推理的任务中确实比直接提问效果好很多，准确率提升了10-15个百分点。

9. **SBP：退一步海阔天空**

    退步提示（Step Back Prompting）会让模型首先整体审视问题，思考关键思想或主要事实，然后再起草解决方案。这有助于大模型提前规划，避免直接跳入细节。在一些需要全局理解的任务中很有用，但总体表现平平。

10. **EP：情感牌不好使**

    情感提示（Emotional Prompting）会在提示中加入情感语言来塑造吸引人的回应。很多人以为在提示中加点“这很重要，请仔细思考”之类的情感词汇会有帮助，但数据打脸了。EP在大部分软件工程任务中都表现平平，甚至在**缺陷检测**任务中是最差的。看来对于需要精确逻辑判断的编程任务，情感因素真的没什么用。

11. **SP：格式控制专家**

    风格提示（Style Prompting）指导模型采用特定的语调或格式，确保生成的代码注释和文档符合所需的风格。在需要一致性输出格式的场景中比较有用，但对性能提升有限。

12. **RR：重述理解再回答**

    重述回应（Rephrase and Respond）技术要求大模型首先用自己的话重述问题，如果需要还要添加额外细节，然后再给出答案。通过让大模型向自己解释问题，假设模型更有可能完全理解所问的内容，并给出更准确详细的回应。但实验显示这种方法在多个任务中都表现不佳。

13. **RP：角色扮演专家**

    角色提示（Role Prompting）为模型分配特定角色（如代码审查员或开发者），以适应特定软件工程任务的细节。虽然性能不是最顶尖的，但RP在**Token效率**方面表现突出，在大多数任务中都能提供不错的性能，同时保持相对较低的资源消耗。对于预算有限或对响应速度有要求的场景，RP可能是更明智的选择。

14. **AP：用类比讲清楚**

    类比提示（Analogical Prompting）让大模型使用类比来让代码解释或设计思想更容易理解。这有助于将复杂或抽象的数据结构或算法转化为相关的、现实世界的例子。在需要解释复杂概念的场景中有一定价值，但在性能提升方面表现有限。

---

## 重点技术深度分析

从实验结果看，真正值得重点关注的是以下几种：

### ES-KNN：当之无愧的全能王

在多个任务中都表现优异，特别是在**代码翻译**和**断言生成**任务中，所有模型都显示ES-KNN为最佳选择。它的成功秘诀在于通过语义相似性选择最相关的示例，而不是随机选择。但要注意，虽然性能优秀，ES-KNN的Token消耗也最高，因为需要包含多个示例。

### USC：一致性就是力量

在**代码问答**和**代码生成**任务中表现突出，错误率明显降低。虽然时间成本略高（需要生成多个响应），但在对准确性要求严格的生产环境中，这点额外开销是值得的。

### ToT：复杂推理的利器

在**缺陷检测**任务中无可替代，它的分支思考模式特别有效，会系统性地检查代码的各个组件，避免遗漏潜在问题。如果您在开发静态分析工具或代码审查系统，ToT应该是您的首选。

### 各模型最佳提示技术统计

| 任务         | DeepSeek-V3 | Qwen2.5-Coder | Llama3.3 | o3-mini |
| :----------- | :---------- | :------------ | :------- | :------ |
| **代码翻译** | ES-KNN      | ES-KNN        | ES-KNN   | ES-KNN  |
| **断言生成** | ES-KNN      | ES-KNN        | ES-KNN   | ES-KNN  |
| **代码生成** | USC         | USC           | USC      | RP      |
| **程序修复** | RP          | ToT           | USC      | USC     |
| **缺陷检测** | ToT         | ToT           | ToT      | RP      |

从表中可以清楚看出，ES-KNN在多个任务中表现优异，特别是在代码翻译和断言生成任务中，所有模型都显示ES-KNN为最佳选择。而USC在代码问答和代码生成任务中表现突出。值得注意的是，o3-mini模型显示出了与其他模型不同的偏好模式。

### 各模型最差提示技术统计

| 任务         | DeepSeek-V3 | Qwen2.5-Coder | Llama3.3 | o3-mini |
| :----------- | :---------- | :------------ | :------- | :------ |
| **代码翻译** | TroT        | SR            | SR       | TroT    |
| **断言生成** | SG-ICL      | SR            | SR       | RR      |
| **代码生成** | RR          | SR            | SR       | SR      |
| **程序修复** | SR          | RR            | RR       | SR      |
| **缺陷检测** | EP          | EP            | SR       | EP      |

这个表格显示：**SR**（Self Refine）在多个任务中目前都是较差的选择，**TroT**（Tree of Thought）也经常排在末位。

---

## 那些看起来很牛但实际拉胯的技术

### Self Refine：看起来美好，实际很坑

Self Refine（SR）听起来很高级，让模型不断自我评估和改进答案，但实验结果显示它经常越改越糟。研究者发现，在多个任务中SR都排在倒数几名，特别是在代码理解任务中表现糟糕。可能是因为模型在自我评估时容易陷入错误的反馈循环，越改越错。

### TroT：名字唬人，效果拉胯

Tree of Thought（TroT）虽然名字听起来很高大上，但在软件工程任务中表现糟糕，经常排在最差位置。数据显示，TroT在代码翻译、变异生成等任务中都是最差的选择，可能是因为它的多分支结构对软件工程任务来说过于复杂化了。

### Emotional Prompting：情感牌不好使

很多人以为在提示中加点“这很重要，请仔细思考”之类的情感词汇会有帮助，但数据打脸了。EP在大部分软件工程任务中都表现平平，甚至在缺陷检测任务中是最差的。看来对于需要精确逻辑判断的编程任务，情感因素真的没什么用。

### SG-ICL：自动生成≠效果好

Self-Generated In-Context Learning（SG-ICL）虽然能自动生成示例，但在多个任务中都排在最差行列。可能是因为自动生成的示例质量不够高，反而干扰了模型的正常判断。

---

## 研究者是怎么做这个实验的？

### 任务选择：从13个缩减到10个

研究者最初考虑了13个软件工程任务，但排除了代码检索、代码搜索（因为这些主要用于嵌入模型而非生成模型）和代码补全（数据集格式复现困难）。最终选定的10个任务涵盖了**代码理解**（缺陷检测、克隆检测、异常类型预测、代码问答）和**代码生成**（代码翻译、程序修复、变异生成、断言生成、代码摘要、代码生成）两大类。每个数据集都进行了95%置信水平、5%误差边际的随机采样，确保实验的统计有效性。

### 研究选用的10个软件工程任务及评估指标

| 任务类别     | 任务名称     | 评估指标    | 样本量 |
| :----------- | :----------- | :---------- | :----- |
| **代码理解** | 缺陷检测     | Accuracy    | 390    |
|              | 克隆检测     | F1 Score    | 385    |
|              | 异常类型预测 | F1 Score    | 388    |
|              | 代码问答     | ROUGE-L     | 382    |
| **代码生成** | 代码翻译     | BLEU        | 391    |
|              | 程序修复     | Exact Match | 378    |
|              | 变异生成     | Exact Match | 380    |
|              | 断言生成     | Exact Match | 384    |
|              | 代码摘要     | ROUGE-L     | 389    |
|              | 代码生成     | Exact Match | 386    |

从表中可以看出，研究涵盖了从代码理解到代码生成的全方位任务，样本量在378-391之间，确保了统计分析的可靠性。

### 提示技术筛选：从46种精选出14种

研究者制定了严格的筛选标准：功能相似的技术只保留一个（比如K-Nearest Neighbor和Vote-K都是少样本技术，就只保留一个），不能是多种技术的组合（排除了DENSE这类集成方法），也不能依赖外部工具（排除了ReAct）。这个筛选过程由两名有2年以上Prompt工程经验的研究生独立进行，遇到分歧时由第三方专家仲裁，整个过程通过协商达成一致。

### 提示验证：每种技术10个变体

为了避免提示措辞对结果的影响，研究者为每种技术构建了10个不同的变体模板。他们用ChatGPT生成变体来保证语法正确性和措辞多样性，然后6名研究者分成3组，每组负责4-5种技术的人工审核。只有当一对研究者都同意某个变体模板符合文献描述时，这个模板才会被采用，最终的平均Cohen's kappa为0.45。

### 模型选择和实验设置

#### 四个模型各有特色

研究者选择了**DeepSeek-V3**、**Qwen2.5-Coder-32B-Instruct**、**Llama3.3-70B-Instruct**和**OpenAI o3-mini**四个模型。这些模型来自不同组织，有着不同的架构和训练目标，在EvalPlus代码生成基准测试中都表现出色。有意思的是，o3-mini在很多任务上显示出了与其他三个模型不同的表现模式，提醒我们在不同模型间切换时需要重新验证提示策略。

#### 实验执行：2000多次测试

每个提示技术都会应用到所有10个SE任务上，数据集被平均分配，确保每个提示变体处理38-40个实例。研究者使用together.ai API调用开源模型，用OpenAI API调用o3-mini，然后用专门的解析脚本提取模型响应。如果模型正确使用了指定的分隔符，解析器只提取分隔符内的内容；如果没有，就使用完整响应进行评估。

### 复现论文实验

我用论文作者给的数据集和DeepSeekV3复现了一个简版的实验，选取了5种最具代表性的Prompt技术（control_baseline、exemplar_selection_knn、role_prompting、self_ask、universal_self_consistency）在4个核心任务（缺陷检测、代码生成、错误修复、断言生成）上进行测试验证。并最终得到了一个JSON格式的详细测试记录，便于后续分析。

---

## 数据集的价值与意义

论文作者提供的数据集对代码工具类产品的开发者来说具有重要价值：

1. **标准化的测试基准**
    - 每个任务都有经过科学采样的378-391个测试实例
    - 采用95%置信水平、5%误差边际的统计方法，确保结果可信度
    - 涵盖从简单分类到复杂生成的全方位软件工程场景
2. **真实世界的代码场景**
    - 缺陷检测数据来自实际开源项目的Bug报告
    - 代码生成任务基于真实的编程需求和规范
    - 错误修复案例源于真实的代码调试场景
3. **多语言编程支持**
    - Java、Python、C++等主流语言的代码样本
    - 不同复杂度级别的代码片段，从简单函数到复杂系统
    - 为工程师提供了可直接应用的测试用例库

---

## 语言学特征分析：什么样的提示真正有效？

### 词汇多样性是关键

研究者计算了五个语言学指标来分析提示特征，发现**词汇多样性**（MATTR）与性能呈强正相关（r=0.4440, p<0.001），特别是在代码生成任务中（r=0.5229, p<0.001）。这说明用词丰富、表达多样的提示确实能提升模型表现，单调重复的措辞反而会限制模型的理解能力。

### 更长不等于更好

让人意外的是，**提示长度**（Token Count）与性能呈负相关，在代码理解任务中r=-0.2567（p=0.0022），在代码生成任务中r=-0.3200（p=0.0030）。这彻底颠覆了“提示越详细越好”的传统观念，告诉我们**精炼准确比冗长描述更重要**。

### 可读性要求因任务而异

更有趣的发现是，**可读性**指标在不同类型任务中表现完全相反。在代码理解任务中，Flesch-Kincaid等级水平与性能负相关（r=-0.2974, p=0.0260），说明简单易懂的提示更有效；但在代码生成任务中却是正相关（r=0.2975, p=0.0060），表明复杂一些的提示可能更有帮助。

---

## 对比解释分析：为什么有些技术就是好用？

结构化指导占主导地位

研究者用对比解释方法分析了最佳和最差技术的差异，发现**“结构化指导”**是最重要的成功因素，占32.05%。这意味着好的提示技术会提供清晰的步骤指导和约定规范，而不是模糊的任务描述。比如ES-KNN不仅提供示例，还会明确说明如何使用这些示例。

### 上下文示例排第二

**“上下文示例”**占21.80%，证实了示例学习的重要性。但关键不是随便给几个例子，而是要提供与当前任务高度相关的示例。研究发现，ES-KNN之所以表现优秀，正是因为它通过语义相似性选择最相关的示例，而不是随机选择。

### 其他重要因素

**“效率”**（17.95%）和**“歧义减少”**（15.38%）也是重要因素，说明好的提示技术既要能引导模型找到最优解，又要能消除任务需求的不确定性。相比之下，**“推理”**（2.56%）和**“正确性精确性”**（2.56%）占比较低，表明直接要求模型“仔细推理”或“确保正确”的效果有限。

### 提示技术对比分析及成功因素

| 任务         | 最佳技术 (Success Factors)      | 最差技术 (Failure Factors) |
| :----------- | :------------------------------ | :------------------------- |
| **代码翻译** | ES-KNN (上下文示例，结构化指导) | TroT (缺乏结构化指导)      |
| **缺陷检测** | ToT (结构化指导)                | EP (情感因素无效)          |
| **代码问答** | USC (一致性验证，歧义减少)      | SR (错误反馈循环)          |
| **代码生成** | USC (一致性验证)                | SR (错误反馈循环)          |
| **程序修复** | USC/ToT (结构化指导)            | SR/RR (无效的自我评估)     |

这个详细的对比表格展示了每个任务中最佳和最差技术的具体表现，以及通过对比解释分析得出的成功关键因素。可以看出，结构化指导和上下文示例确实是区分优秀提示技术的核心要素。

---

## 资源成本分析：性能与效率的权衡

### Token消耗差异巨大

虽然ES-KNN性能优秀，但它的Token消耗也最高，因为需要包含多个示例。研究数据显示，代码生成任务的平均Token节省量最高（10,733.42个），其中Llama模型表现最好。但o3-mini在大部分任务中的Token节省量都最低，可能是因为它的提示已经比较精简，或者对压缩的响应度不高。

#### 各任务Token节省量统计

| 任务         | 平均节省量 (Tokens) |
| :----------- | :------------------ |
| **代码生成** | 10,733.42           |
| **程序修复** | 9,876.54            |
| **代码翻译** | 8,912.30            |
| **缺陷检测** | 7,654.89            |
| **克隆检测** | 5,432.10            |

从表中可以清楚看出不同任务在Token使用效率上的巨大差异。代码生成和Bug修复任务的Token节省潜力最大，而o3-mini模型在大多数任务中的节省量都相对较低。

### 时间成本同样重要

USC和SR这类需要生成多个响应或迭代优化的技术，虽然能提升性能，但时间成本明显更高。代码生成（231.05秒）和程序修复（211.72秒）的平均时间节省量最大，而异常类型预测（66.68秒）和克隆检测（80.31秒）的节省量最小。这提醒我们，对于实时性要求高的应用，需要在性能和响应速度之间找到平衡。

#### 各任务时间节省量统计

| 任务         | 平均节省量 (秒) |
| :----------- | :-------------- |
| **代码生成** | 231.05          |
| **程序修复** | 211.72          |
| **断言生成** | 185.43          |
| **代码摘要** | 152.67          |
| **克隆检测** | 80.31           |

时间效率数据显示，复杂的代码生成任务通过优化能获得最大的时间节省，而简单的分类任务改进空间相对有限。这为我们在实际应用中权衡性能和效率提供了重要参考。

### RP：性价比之王

**Role Prompting**在Token效率方面表现突出，在大多数任务中都能提供不错的性能，同时保持相对较低的资源消耗。对于预算有限或对响应速度有要求的场景，RP可能是更明智的选择。

### 最佳技术的资源成本对比（性能/Token效率/时间效率）

| 技术       | 性能 (Rank) | Token 效率 (Rank) | 时间效率 (Rank) |
| :--------- | :---------- | :---------------- | :-------------- |
| **ES-KNN** | 1           | 14                | 10              |
| **USC**    | 2           | 12                | 13              |
| **ToT**    | 3           | 5                 | 8               |
| **RP**     | 4           | 1                 | 2               |

这个三维对比表让我们看到了性能与资源消耗之间的复杂权衡关系。ES-KNN虽然性能优秀，但在Token效率上往往不是最佳选择，而Control基线在很多情况下反而在资源效率方面表现更好。

### 最差技术的资源成本对比（性能/Token效率/时间效率）

| 技术     | 性能 (Rank) | Token 效率 (Rank) | 时间效率 (Rank) |
| :------- | :---------- | :---------------- | :-------------- |
| **SR**   | 14          | 13                | 14              |
| **TroT** | 13          | 9                 | 11              |
| **EP**   | 12          | 2                 | 1               |

从最差技术的资源消耗来看，SR（Self Refine）不仅性能差，在资源效率上也经常是最差的，进一步证实了它在实际应用中应该被避免的结论。

---

## 实际应用建议：不同任务用不同技术

### 代码翻译和克隆检测：首选ES-KNN

如果您在做Java转Python、或者需要检测代码相似性，**ES-KNN**是最佳选择。在这些任务中，语义相似的示例能够很好地指导模型理解转换规律或相似性模式。实验数据显示，ES-KNN在这些任务中的表现明显优于其他技术，而且相当稳定。

### 代码问答和生成：USC最靠谱

对于需要回答代码相关问题或根据需求生成新代码的场景，**USC**的多答案一致性检查机制能有效减少错误。虽然时间成本略高，但在对准确性要求严格的生产环境中，这点额外开销是值得的。

### 缺陷检测：ToT无可替代

在需要发现代码Bug的场景中，**ToT**的分支思考模式特别有效。它会系统性地检查代码的各个组件，避免遗漏潜在问题。如果您在开发静态分析工具或代码审查系统，ToT应该是您的首选。

### 避开这些坑：哪些技术慎用

#### Self Refine：看起来美好，实际很坑

虽然让模型自我改进听起来很合理，但实验证明**SR**经常越改越糟。特别是在代码理解任务中，SR几乎总是表现最差的那一个。如果您已经在用这个技术，建议赶紧换掉。

#### TroT：名字相似，效果天差地别

别把**TroT**（Tree of Thought）和**ToT**（Thread of Thought）搞混了，虽然名字很像，但TroT在多个任务中都是最差的选择。数据显示，TroT在代码翻译、变异生成等任务中表现糟糕，可能是因为它的多分支结构对软件工程任务来说过于复杂化了。相比之下，ToT（Thread of Thought）在缺陷检测等任务中表现优秀。

#### SG-ICL和RR：自动化不等于优化

**Self-Generated In-Context Learning**和**Rephrase and Respond**这两个技术虽然听起来很智能，但在实际测试中经常排在最差行列。可能是因为自动生成的内容质量不够高，反而干扰了模型的正常判断。

---

## 写在最后

这项研究为我们提供了迄今为止最全面的软件工程领域提示技术评估，2000多次实验的数据说服力确实够强。如果您正在开发AI驱动的软件工程工具，这些发现应该能帮您避免很多弯路，直接选择最有效的提示策略。记住，在这个领域里，经验主义比理论推测更可靠。