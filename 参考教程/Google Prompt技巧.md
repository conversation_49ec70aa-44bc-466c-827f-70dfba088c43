## 1. 零样本提示 (Zero-shot Prompting)

* **概念**: 直接向 LLM 提出问题或下达指令，不提供任何具体的输入输出示例。
* **优点**: 最简单、最快速的提示方式。
* **缺点**: 对于复杂任务、需要特定输出格式或模型不太熟悉的问题，效果可能不佳或不稳定。
* **适用场景**: 简单问答、常见的指令（如翻译、总结）、模型已经非常擅长处理的任务。
* **示例**:
    * `将“你好”翻译成西班牙语。`
    * `总结以下段落的主要观点：[段落文本]`

## 2. 少样本提示 (Few-shot Prompting / One-shot Prompting)

* **概念**: 在提出实际任务之前，先向 LLM 提供一个（One-shot）或多个（Few-shot）完整的输入输出示例，让模型学习期望的模式或格式。
* **优点**: 能有效引导模型按照特定格式、风格或逻辑进行输出；提高复杂任务的准确性；适用于需要模型“依葫芦画瓢”的场景。
* **缺点**: 需要精心设计高质量的示例；增加了提示的长度和成本；模型可能对示例的微小偏差很敏感。
* **适用场景**: 需要特定输出格式（如JSON、代码、固定结构）、进行风格模仿、完成需要特定推理模式的任务、小样本学习。
* **示例**:
    * `输入：我想订一个小号芝士披萨。 输出：{"size": "small", "type": "normal", "ingredients": ["cheese"]} \n 输入：来个大号的，加辣肠和蘑菇。 输出：` (模型应按格式输出 JSON)
    * `问题：香蕉是什么颜色？ 答案：黄色 \n 问题：天空是什么颜色？ 答案：` (模型应输出“蓝色”)

## 3. 系统提示 (System Prompting)

* **概念**: 在主提示之外或在提示的开头，** 设定 LLM 的整体行为规范、任务目标、输出约束或扮演的背景角色**（广义上也可包含角色提示的部分功能）。
* **优点**: 强制模型遵守特定规则或格式；可以设定安全边界或语气风格；明确任务框架。
* **缺点**: 如果规则过于严格或冲突，可能限制模型的发挥；增加了设置的复杂度。
* **适用场景**: 要求固定输出格式（JSON、特定结构）、设定回答的安全或道德约束、定义模型的总体任务或能力范围。
* **示例**:
    * `指令：将评论分类为正面或负面。规则：只允许输出“正面”或“负面”这两个词。 评论：这部电影太棒了！`
    * 指令：你是一个乐于助人的 AI 助手，回答问题时要保持友好和耐心。 用户：请解释一下光合作用。`

## 4. 角色提示 (Role Prompting)

* **概念**: 明确指示 LLM 扮演一个特定的角色、身份或人设，并以该角色的口吻、知识和视角进行回应。
* **优点**: 使输出更具个性、风格和针对性；可以模拟特定专业人士或角色的行为；增强交互的趣味性。
* **缺点**: 可能导致回答带有角色偏见而非客观事实；模型能否准确扮演角色取决于其能力和训练数据。
* **适用场景**: 创意写作、模拟对话（客服、面试）、教育（扮演历史人物）、内容生成（特定风格）。
* **示例**:
    * `扮演一位旅行向导，向我推荐三个在巴黎的必去景点。`
    * `假设你是一位经验丰富的厨师，请教我如何完美地煎一块牛排。`

## 5. 上下文提示 (Contextual Prompting)

* **概念**: 在提示中提供与当前具体任务或对话直接相关的背景信息、历史记录或特定数据。
* **优点**: 提高回答的相关性和准确性；使对话更连贯；让模型能利用当前情境信息。
* **缺点**: 需要准确判断并提供相关上下文；可能显著增加提示长度。
* **适用场景**: 多轮对话（提供聊天历史）、基于特定文档的问答、需要结合用户信息的个性化推荐、对特定数据进行分析或总结。
* **示例**:
    * `背景：你正为80年代复古街机游戏博客写作。 任务：建议3个文章主题。`
    * `上下文：用户刚才问了如何用 Python 读取文件。 任务：现在请解释一下 Python 中 'with open(...)' 语句的作用和好处。`

## 6. 回退提示 (Step-back Prompting)

* **概念**: 分两步进行。第一步，问一个与最终任务相关的、但更通用或抽象的问题。第二步，将第一步 AI 的回答作为上下文，再提出具体的任务。
* **优点**: 激活模型更深层的知识和推理能力；产生更深入、更少泛泛之谈的回答；提高复杂问题处理的准确性；也可用于帮助用户理清自己的思路。
* **缺点**: 至少需要两次交互或一个更复杂的组合提示；增加了时间和成本。
* **适用场景**: 需要深度推理或基于原则的回答；直接提问易得肤浅答案时；复杂创意生成；需要减少偏见时；用户自身对需求或概念不清时。
* **示例**:
    * `第一步：解释什么是市场细分？ 第二步：背景：市场细分是将市场划分为具有相似特征的子群体的过程。任务：请为一款新的健身 App 进行市场细分，并描述至少两个目标用户群体。`
    * `第一步：在谈判中，有哪些常用的双赢策略？ 第二步：背景：双赢谈判策略包括关注利益而非立场、创造多种选择等。任务：假设我正在与供应商谈判长期合同的价格，请运用双赢策略，提出两个具体的谈判建议。`

## 7. 思维链提示 (Chain of Thought - CoT)

* **概念**: 引导 LLM 在给出最终答案前，先输出中间的推理步骤或思考过程。通常通过加入“让我们一步一步地思考”等指令或提供带步骤的示例（少样本 CoT）实现。
* **优点**: 显著提高需要多步推理（数学、逻辑）任务的准确性；提供过程可解释性；有助于发现和调试错误。
* **缺点**: 输出内容变长，增加成本和延迟；推理步骤中仍可能出错。
* **适用场景**: 数学应用题、逻辑推理题、复杂问题求解、需要解释步骤的任务、代码生成中的逻辑分解。
* **示例**:
    * `我3岁时伴侣年龄是我3倍，现在我20岁，伴侣多大？请一步一步思考。`
    * `问题：一个农场有鸡和兔子共 35 头，共有 94 只脚。问鸡和兔子各多少只？请列出解题步骤。`

## 8. 自洽性提示 (Self-Consistency)

* **概念**: 对同一个（通常是 CoT）提示，使用较高的温度进行多次（≥3次）推理，生成多个不同的推理路径和答案，最后通过多数投票选择最频繁出现的答案。
* **优点**: 比单次 CoT 更准确、更鲁棒，尤其适用于有多种解法或推理路径的问题。
* **缺点**: 计算成本和时间成本显著增加（需要多次运行）；答案提取和投票机制可能需要额外处理。
* **适用场景**: 对准确性要求极高的复杂推理任务；标准 CoT 表现不稳定的情况；追求最佳性能（如刷榜）。
* **示例**:
    * `将邮件分类为重要/不重要，一步步思考。（运行5次，Temp=0.7，统计最终分类结果）`
    * `解决这个复杂的逻辑谜题：[谜题描述]。请逐步推理。（运行 N 次，高温度，选择出现次数最多的最终答案）`

## 9. 思维树提示 (Tree of Thoughts - ToT)

* **概念**: CoT 的泛化。允许 LLM 在推理的每一步探索多个（>1）分支路径（想法），形成一个树状结构。包含对中间想法的评估和可能的剪枝。
* **优点**: 能处理需要广泛探索、规划、回溯的极其复杂的问题；比 CoT 或 Self-Consistency 更具策略性。
* **缺点**: 实现非常复杂，需要额外的控制框架和算法（评估、搜索）；计算成本极高。
* **适用场景**: 复杂规划（如下棋）、需要尝试多种可能性的问题解决、需要系统性探索的创意生成或数学证明。
* **示例**: (通常需要专用框架实现，难以用简单文本展示)
    * (概念) `为解决[某复杂问题]，请探索至少三种不同的策略路径，并在每一步评估子路径的有效性。`
    * (概念) `找到走出这个迷宫的最佳路径：[迷宫规则]。请在每个决策点考虑所有可能的移动，并评估其潜力。`

## 10. ReAct 提示 (Reason & Act)

* **概念**: 将 LLM 的推理（Reason）能力与执行动作（Act，通过调用外部工具如搜索、计算器、API 等）的能力结合起来，形成“思考→行动→观察→思考...”的循环。
* **优点**: 能获取和利用实时、外部信息，克服知识库限制；能执行计算或与其他系统交互；是构建 AI Agent 的基础。
* **缺点**: 需要集成外部工具，实现复杂；有延迟和额外成本（API 调用）；需要处理工具错误。
* **适用场景**: 需要最新信息的问答、需要外部数据查询或计算的任务、事实核查、能被分解为“思考+查证/操作”的任务、自动化工作流。
* **示例**:
    * `Metallica 成员共有多少孩子？（使用搜索工具查询）`
    * `当前美元兑换欧元的汇率是多少？请计算 500 美元等于多少欧元。（需要汇率查询工具+计算器工具）`

## 11. 自动提示工程 (Automatic Prompt Engineering - APE)

* **概念**: 利用 LLM 来自动生成、测试和优化用于其他（或同一个）LLM 的提示指令。
* **优点**: 减少手动设计提示的繁琐工作；可能发现比人类设计的更优提示；有助于为新任务快速生成初始提示。
* **缺点**: 计算成本高（尤其是评估环节）；需要定义清晰的评估指标和数据集；效果依赖于 APE 方法和元提示。
* **适用场景**: 需要为有明确评估标准的任务优化提示；需要为特定意图生成大量不同表达方式；探索全新任务的最佳提示。
* **示例**: (通常需要专用框架实现)
    * (概念) `目标任务：将英文技术文档翻译成中文。请生成 5 种不同的指令提示，并通过 BLEU 分数在[测试集]上评估它们的效果，选出最优提示。`
    * (概念) `基于这些输入输出示例对：[提供若干示例]，请推断出能够最好地描述这个任务的指令提示。`