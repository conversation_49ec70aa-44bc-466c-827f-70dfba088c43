# AI 思维模型指令系统说明

本说明旨在定义一系列指令标签，用于引导 AI 在回应时采用特定的思维模型。通过在提问时使用这些标签，可以获得更深刻、更结构化、更有创意的回答，从而将 AI 从一个简单的问答工具，转变为一个强大的思维伙伴。

---

## 系统提示词 (System Prompt) 指令定义

**以下是可直接复制到 AI 系统提示词中的指令定义文本：**

当用户输入以斜杠 (/) 开头的特定标签时，请严格遵循以下思维模式进行回应：
* **/mq (Meta-Questioning):** 收到此指令时，不要直接回答用户的问题。你的任务是扮演一个顾问或教练，通过反向提问的方式，帮助用户澄清思路、定义问题、探索其提问背后的真实意图和隐藏假设。
* **/og (Underlying Logic):** 收到此指令时，你的核心任务是分析现象背后的底层逻辑和核心规律。穿透表面，解释系统（如商业、社会、用户行为）是如何运作的，找到其内部不变的驱动因素。
* **/first (First Principles):** 收到此指令时，请运用第一性原理。将问题拆解至最基础、不可再分的要素（如物理、数学或人性的公理），然后基于这些基本要素，自下而上地、不带任何经验预设地进行推理和重构，以得出全新的结论或解决方案。
* **/cu (Curiosity):** 收到此指令时，请扮演一个充满好奇心的探索者。从多个、甚至意想不到的角度（如跨学科、逆向思考）来分析问题，自由联想，发掘事物之间新颖的联系和可能性。
* **/story (Storytelling):** 收到此指令时，请将核心内容用比喻、类比或故事的形式讲述出来。你的目标是将复杂、抽象或枯燥的信息，转化为生动、形象、易于理解和记忆的情境。
* **/step (Step-by-step):** 收到此指令时，你的回答必须是结构化、分步骤的行动指南。使用编号列表（1, 2, 3...）清晰地呈现每一步，确保内容具有逻辑性和可操作性。
* **/all (All-in-one):** 收到此指令时，你需要自主判断并灵活融合上述多种思维模式来提供一个全面的回答。根据问题的不同阶段，有机地运用最适合的工具，而非机械地叠加。

---

## 指令用法说明

以下指令按照在解决一个复杂问题时，通常情况下的使用先后顺序排列。

### 1. `/mq` - 元提问 (Meta-Questioning)

* **何时使用**：当问题非常宏大、模糊，或者您感觉“脑袋里一团浆糊”，不确定真正的问题是什么时。这是开启深度思考的第一步。
* **作用**：指令 AI 不要直接回答问题，而是像一位顾问或教练，通过向您**反向提问**，来帮助您澄清思路、定义问题边界、找到隐藏的假设，最终将一个模糊的大问题，聚焦成一个或多个清晰、可解决的具体问题。

### 2. `/og` - 底层逻辑 (Underlying Logic)

* **何时使用**：当您想理解一个现象或系统背后的根本原因和运行规律时。适用于分析商业模式、社会现象、用户行为等复杂系统。
* **作用**：指令 AI 穿透表面现象，专注于分析事物内部各元素之间的相互关系，挖掘其背后的核心驱动因素、商业逻辑或行为模式，找到“不变的规律”。

### 3. `/first` - 第一性原理 (First Principles)

* **何时使用**：当您需要为某个已明确的问题寻找颠覆性或创新的解决方案时，或者需要对一个事物进行最根本的成本、结构或可行性分析时。
* **作用**：指令 AI 将问题彻底拆解为最基础、最核心、不可再分的“事实”或“物理要素”（如物理定律、人性需求、数学公理），然后基于这些最基本的要素，自下而上地、不带任何经验预设地重新推演，构建出全新的解决方案。

### 4. `/cu` - 好奇心驱动 (Curiosity)

* **何时使用**：当您希望跳出常规思维框架，从多个意想不到的角度来探索一个问题或概念时。
* **作用**：指令 AI 扮演一个充满好奇心的探索者角色，不受限于传统答案，从多学科、多角度（例如，从经济学、心理学、历史、艺术等不同视角）对问题进行自由联想和探索，以发掘其全新的可能性和有趣的连接。

### 5. `/story` - 故事化 (Storytelling)

* **何时使用**：当您需要理解一个复杂的概念、抽象的理论，或者希望与某个观点建立情感连接时。
* **作用**：指令 AI 将复杂的逻辑、枯燥的数据或抽象的概念，用一个生动的比喻、形象的故事或具体的场景来包装和转述。这有助于降低认知负荷，增强理解和记忆。

### 6. `/step` - 步骤化 (Step-by-step)

* **何时使用**：当您已经完成了分析和理解，需要将想法转化为具体行动时。这是将思考落地的最后一步。
* **作用**：指令 AI 提供一个结构清晰、逻辑严谨、可操作性强的行动指南或计划。输出内容会以编号列表（1, 2, 3...）的形式呈现，确保每一步都清晰明了。

### 7. `/all` - 融合模式 (All-in-one)

* **何时使用**：当您面对一个综合性极强的复杂问题，希望 AI 能够灵活地、有机地融合上述所有或多种思维模式，提供一个全面且有深度的回答时。
* **作用**：指令 AI 自主判断在回答的不同阶段应采用何种思维模式。它可能会先用 `/mq` 澄清，然后用 `/first` 分析，接着用 `/story` 解释，最后用 `/step` 总结，而非机械地叠加每一种方法。

---

## 示例：如何使用指令系统解决“育儿”难题

以下示例展示了当用户面对一个宏大而模糊的“育儿”问题时，如何通过依次使用指令标签，将“一团浆糊”的思绪，逐步转化为清晰的认知和可行的方案。

### 第 1 步：感到困惑，向 AI 求助以理清思路

**用户输入：**
> /mq 我想知道该如何育儿，现在脑袋里一团浆糊。

**AI 的作用：**
AI 不会直接提供育儿建议，而是会像一名顾问一样反向提问，帮助用户聚焦。例如：“您孩子的年龄是多大？您最感到困扰的具体场景是什么？是关于学习、情绪还是日常习惯？”

### 第 2 步：锁定具体问题，探究根本原因

**用户输入 (在与 AI 互动后明确了问题)：**
> /first 我的女儿三岁，最近经常在超市因为得不到想要的玩具而大声哭闹。请帮我分析一下，这个年龄段孩子发生这种行为的根本原因是什么？

**AI 的作用：**
AI 会从最基础的生理学（大脑发育不完全）、心理学（自我意识萌发）和沟通学（语言能力有限）等角度，拆解“哭闹”行为背后的根本原因。

### 第 3 步：将理论转化为共情

**用户输入：**
> /story 请用一个生动的比喻，向我解释“三岁孩子的大脑前额叶皮层发育不完全”这个概念。

**AI 的作用：**
AI 会创造一个易于理解的故事，例如将孩子的大脑比作“一个只有油门、刹车很弱的情绪小跑车”，帮助用户更好地理解和接纳孩子的情绪风暴。

### 第 4 步：获取可执行的行动方案

**用户输入：**
> /step 基于我们已经明确的原因，请提供一个清晰、可操作的应对指南，分‘事前预防’、‘事中应对’和‘事后复盘’三部分。

**AI 的作用：**
AI 会提供一份结构清晰的行动清单，详细列出在不同阶段可以采取的具体步骤，例如事前如何设定预期，事中如何共情并设立边界等。

通过这一系列指令，用户将一个模糊的焦虑，成功转化为了深刻的理解和具体的行动策略。